<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek Chat - 未来聊天体验</title>
    <style>
        :root {
            /* 主题色彩 - 赛博朋克风格 */
            --primary-color: #00ffff;
            --primary-hover: #00cccc;
            --secondary-color: #ff0080;
            --accent-color: #ffff00;
            --success-color: #00ff00;
            --warning-color: #ff8000;
            --error-color: #ff0040;

            /* 深色主题 */
            --bg-primary: #0a0a0f;
            --bg-secondary: #1a1a2e;
            --bg-tertiary: #16213e;
            --bg-glass: rgba(26, 26, 46, 0.8);
            --bg-card: rgba(22, 33, 62, 0.9);

            /* 文字颜色 */
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --text-accent: #00ffff;
            --text-muted: #666666;

            /* 边框和阴影 */
            --border-primary: rgba(0, 255, 255, 0.3);
            --border-secondary: rgba(255, 255, 255, 0.1);
            --shadow-neon: 0 0 20px rgba(0, 255, 255, 0.5);
            --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.3);
            --shadow-card: 0 4px 16px rgba(0, 0, 0, 0.4);

            /* 动画时长 */
            --transition-fast: 0.2s;
            --transition-normal: 0.3s;
            --transition-slow: 0.5s;

            /* 圆角 */
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 20px;
            --radius-xl: 30px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            color: var(--text-primary);
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            background-attachment: fixed;
            display: flex;
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* 动态背景粒子效果 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 0, 0.05) 0%, transparent 50%);
            animation: backgroundPulse 8s ease-in-out infinite;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes backgroundPulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.6; }
        }

        /* 全屏加载动画 */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            animation: fadeOut 2s ease-in-out 3s forwards;
        }

        .loading-logo {
            font-size: 3rem;
            font-weight: bold;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--accent-color));
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 2s ease-in-out infinite, logoFloat 3s ease-in-out infinite;
            margin-bottom: 2rem;
        }

        .loading-text {
            color: var(--text-secondary);
            font-size: 1.2rem;
            margin-bottom: 2rem;
            animation: textPulse 1.5s ease-in-out infinite;
        }

        .loading-bar {
            width: 300px;
            height: 4px;
            background: var(--bg-tertiary);
            border-radius: 2px;
            overflow: hidden;
            position: relative;
        }

        .loading-progress {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 2px;
            animation: loadingProgress 3s ease-in-out forwards;
            box-shadow: 0 0 10px var(--primary-color);
        }

        @keyframes fadeOut {
            to { opacity: 0; visibility: hidden; }
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @keyframes textPulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        @keyframes loadingProgress {
            0% { width: 0%; }
            100% { width: 100%; }
        }

        /* 连接状态指示器 */
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            border-radius: var(--radius-lg);
            background: var(--bg-glass);
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-secondary);
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            z-index: 1000;
            transition: all var(--transition-normal);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: statusPulse 2s ease-in-out infinite;
        }

        .status-connected { background: var(--success-color); }
        .status-connecting { background: var(--warning-color); }
        .status-disconnected { background: var(--error-color); }

        @keyframes statusPulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.6; transform: scale(1.2); }
        }

        /* 主题切换按钮 */
        .theme-toggle {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--bg-glass);
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-primary);
            color: var(--primary-color);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-normal);
            z-index: 1000;
            box-shadow: var(--shadow-neon);
        }

        .theme-toggle:hover {
            transform: scale(1.1) rotate(180deg);
            box-shadow: 0 0 30px var(--primary-color);
        }

        /* 侧边栏样式 - 玻璃态设计 */
        .sidebar {
            width: 320px;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-right: 1px solid var(--border-primary);
            display: flex;
            flex-direction: column;
            height: 100vh;
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.05) 0%, rgba(255, 0, 128, 0.05) 100%);
            pointer-events: none;
        }

        .sidebar-header {
            padding: 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            z-index: 2;
        }

        .new-chat-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--text-primary);
            border: none;
            border-radius: var(--radius-lg);
            padding: 12px 20px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all var(--transition-normal);
            box-shadow: var(--shadow-neon);
            position: relative;
            overflow: hidden;
        }

        .new-chat-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left var(--transition-slow);
        }

        .new-chat-btn:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 0 40px var(--primary-color);
        }

        .new-chat-btn:hover::before {
            left: 100%;
        }

        .new-chat-btn:active {
            transform: translateY(0) scale(0.98);
        }

        .chat-history {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
            position: relative;
            z-index: 2;
        }

        .chat-history::-webkit-scrollbar {
            width: 6px;
        }

        .chat-history::-webkit-scrollbar-track {
            background: transparent;
        }

        .chat-history::-webkit-scrollbar-thumb {
            background: var(--border-primary);
            border-radius: 3px;
        }

        .chat-item {
            padding: 16px;
            border-radius: var(--radius-md);
            cursor: pointer;
            margin-bottom: 8px;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: flex;
            align-items: center;
            gap: 12px;
            background: var(--bg-card);
            border: 1px solid var(--border-secondary);
            transition: all var(--transition-normal);
            position: relative;
            backdrop-filter: blur(10px);
        }

        .chat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            transition: width var(--transition-normal);
            border-radius: var(--radius-md);
            opacity: 0.1;
        }

        .chat-item:hover {
            transform: translateX(8px);
            border-color: var(--border-primary);
            box-shadow: var(--shadow-card);
        }

        .chat-item:hover::before {
            width: 4px;
        }

        .chat-item.active {
            background: var(--bg-tertiary);
            border-color: var(--primary-color);
            box-shadow: var(--shadow-neon);
            color: var(--primary-color);
        }

        .chat-item.active::before {
            width: 4px;
            opacity: 1;
        }

        .sidebar-footer {
            padding: 24px;
            border-top: 1px solid var(--border-primary);
            position: relative;
            z-index: 2;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            background: var(--bg-card);
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-secondary);
            transition: all var(--transition-normal);
        }

        .user-profile:hover {
            border-color: var(--border-primary);
            box-shadow: var(--shadow-card);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: var(--text-primary);
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
        }

        .user-name {
            font-size: 14px;
            flex: 1;
            color: var(--text-primary);
            font-weight: 500;
        }

        .user-menu {
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all var(--transition-normal);
            color: var(--text-secondary);
        }

        .user-menu:hover {
            background: var(--bg-tertiary);
            color: var(--primary-color);
            transform: rotate(90deg);
        }

        /* 主聊天区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100vh;
            background: transparent;
            position: relative;
        }

        .chat-header {
            padding: 24px;
            border-bottom: 1px solid var(--border-primary);
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            position: relative;
            z-index: 10;
        }

        .chat-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, rgba(0, 255, 255, 0.05), rgba(255, 0, 128, 0.05));
            pointer-events: none;
        }

        .chat-title {
            font-weight: 700;
            font-size: 18px;
            color: var(--text-primary);
            position: relative;
            z-index: 2;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .chat-actions {
            display: flex;
            gap: 16px;
            position: relative;
            z-index: 2;
        }

        .chat-action-btn {
            background: var(--bg-card);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-md);
            cursor: pointer;
            color: var(--text-secondary);
            font-size: 16px;
            padding: 12px;
            transition: all var(--transition-normal);
            backdrop-filter: blur(10px);
        }

        .chat-action-btn:hover {
            color: var(--primary-color);
            border-color: var(--border-primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-card);
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 32px;
            display: flex;
            flex-direction: column;
            gap: 32px;
            position: relative;
        }

        .chat-container::-webkit-scrollbar {
            width: 8px;
        }

        .chat-container::-webkit-scrollbar-track {
            background: transparent;
        }

        .chat-container::-webkit-scrollbar-thumb {
            background: var(--border-primary);
            border-radius: 4px;
        }

        .message {
            display: flex;
            gap: 20px;
            max-width: 900px;
            margin: 0 auto;
            width: 100%;
            animation: messageSlideIn 0.5s ease-out;
            opacity: 0;
            animation-fill-mode: forwards;
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            position: relative;
            overflow: hidden;
        }

        .user-avatar {
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
            color: var(--text-primary);
            box-shadow: 0 0 20px rgba(255, 0, 128, 0.4);
        }

        .bot-avatar {
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            color: var(--text-primary);
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.4);
            animation: avatarPulse 3s ease-in-out infinite;
        }

        @keyframes avatarPulse {
            0%, 100% { box-shadow: 0 0 20px rgba(0, 255, 255, 0.4); }
            50% { box-shadow: 0 0 30px rgba(0, 255, 255, 0.8); }
        }

        .message-content {
            flex: 1;
            padding-top: 4px;
        }

        .message-role {
            font-weight: 700;
            margin-bottom: 12px;
            font-size: 16px;
            color: var(--text-accent);
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }

        .message-text {
            line-height: 1.7;
            font-size: 15px;
            color: var(--text-primary);
            background: var(--bg-card);
            padding: 20px;
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-secondary);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .message-text::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        /* 流式文本动画 */
        .streaming-text {
            position: relative;
        }

        .streaming-text::after {
            content: '▋';
            color: var(--primary-color);
            animation: cursorBlink 1s infinite;
        }

        @keyframes cursorBlink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .message-text pre {
            background: var(--bg-tertiary);
            padding: 20px;
            border-radius: var(--radius-md);
            overflow-x: auto;
            margin: 16px 0;
            font-family: "JetBrains Mono", "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
            font-size: 14px;
            border: 1px solid var(--border-primary);
            position: relative;
        }

        .message-text pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .message-text code {
            font-family: "JetBrains Mono", "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
            background: var(--bg-tertiary);
            color: var(--primary-color);
            padding: 4px 8px;
            border-radius: var(--radius-sm);
            font-size: 14px;
            border: 1px solid var(--border-secondary);
        }

        .message-text blockquote {
            border-left: 3px solid var(--primary-color);
            padding-left: 16px;
            margin: 16px 0;
            color: var(--text-secondary);
            background: var(--bg-tertiary);
            padding: 16px;
            border-radius: var(--radius-md);
        }

        .message-actions {
            display: flex;
            gap: 16px;
            margin-top: 16px;
            padding: 12px 0;
        }

        .message-action {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-md);
            cursor: pointer;
            color: var(--text-secondary);
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .message-action::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
            transition: left var(--transition-slow);
        }

        .message-action:hover {
            color: var(--primary-color);
            border-color: var(--border-primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-card);
        }

        .message-action:hover::before {
            left: 100%;
        }

        /* 特殊动画按钮 */
        .like-btn, .dislike-btn {
            position: relative;
            overflow: visible;
        }

        .like-btn:hover {
            color: var(--success-color);
            border-color: var(--success-color);
        }

        .dislike-btn:hover {
            color: var(--error-color);
            border-color: var(--error-color);
        }

        /* 全屏动画效果 */
        .fullscreen-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 9998;
        }

        .like-explosion {
            position: absolute;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, var(--success-color), transparent);
            border-radius: 50%;
            animation: likeExplosion 1s ease-out forwards;
        }

        .dislike-explosion {
            position: absolute;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, var(--error-color), transparent);
            border-radius: 50%;
            animation: dislikeExplosion 1s ease-out forwards;
        }

        @keyframes likeExplosion {
            0% {
                transform: scale(0) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: scale(20) rotate(360deg);
                opacity: 0;
            }
        }

        @keyframes dislikeExplosion {
            0% {
                transform: scale(0) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: scale(20) rotate(-360deg);
                opacity: 0;
            }
        }

        .input-container {
            padding: 32px;
            border-top: 1px solid var(--border-primary);
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            position: relative;
        }

        .input-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, rgba(0, 255, 255, 0.05), rgba(255, 0, 128, 0.05));
            pointer-events: none;
        }

        .input-box {
            max-width: 900px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        .textarea-wrapper {
            position: relative;
            background: var(--bg-card);
            border-radius: var(--radius-xl);
            border: 2px solid var(--border-secondary);
            transition: all var(--transition-normal);
            overflow: hidden;
        }

        .textarea-wrapper::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.05), rgba(255, 0, 128, 0.05));
            pointer-events: none;
        }

        .textarea-wrapper:focus-within {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-neon);
            transform: translateY(-2px);
        }

        .chat-input {
            width: 100%;
            min-height: 64px;
            max-height: 200px;
            padding: 20px 80px 20px 24px;
            border: none;
            background: transparent;
            resize: none;
            font-size: 16px;
            line-height: 1.6;
            outline: none;
            color: var(--text-primary);
            font-family: inherit;
        }

        .chat-input::placeholder {
            color: var(--text-muted);
        }

        .send-btn {
            position: absolute;
            right: 16px;
            bottom: 16px;
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--text-primary);
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-normal);
            box-shadow: var(--shadow-neon);
            position: relative;
            overflow: hidden;
        }

        .send-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left var(--transition-slow);
        }

        .send-btn:hover {
            transform: scale(1.1) rotate(15deg);
            box-shadow: 0 0 40px var(--primary-color);
        }

        .send-btn:hover::before {
            left: 100%;
        }

        .send-btn:active {
            transform: scale(0.95);
        }

        .send-btn:disabled {
            background: var(--bg-tertiary);
            color: var(--text-muted);
            cursor: not-allowed;
            box-shadow: none;
            transform: none;
        }

        .send-btn:disabled::before {
            display: none;
        }

        /* 高级打字指示器 */
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
            padding: 16px 20px;
            background: var(--bg-card);
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-secondary);
        }

        .typing-text {
            color: var(--text-secondary);
            font-size: 14px;
            margin-right: 8px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 10px;
            height: 10px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            animation: typingAnimation 1.4s infinite ease-in-out;
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }

        .typing-dot:nth-child(1) {
            animation-delay: 0s;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typingAnimation {
            0%, 60%, 100% {
                transform: translateY(0) scale(1);
                opacity: 0.6;
            }
            30% {
                transform: translateY(-8px) scale(1.2);
                opacity: 1;
                box-shadow: 0 0 20px rgba(0, 255, 255, 0.8);
            }
        }

        /* 进度条动画 */
        .progress-bar {
            width: 100%;
            height: 3px;
            background: var(--bg-tertiary);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 12px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 2px;
            animation: progressPulse 2s ease-in-out infinite;
            transform-origin: left;
        }

        @keyframes progressPulse {
            0%, 100% { transform: scaleX(0.3); }
            50% { transform: scaleX(0.8); }
        }

        /* 移动端响应式 */
        @media (max-width: 768px) {
            .sidebar {
                position: absolute;
                z-index: 100;
                transform: translateX(-100%);
                width: 280px;
            }
            .sidebar.open {
                transform: translateX(0);
                box-shadow: var(--shadow-glass);
            }
            .main-content {
                width: 100%;
            }
            .message {
                max-width: 100%;
                gap: 12px;
            }
            .chat-container {
                padding: 16px;
                gap: 20px;
            }
            .input-container {
                padding: 16px;
            }
            .theme-toggle, .connection-status {
                display: none;
            }
        }

        /* 高级表格样式 */
        .message-text table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
            font-size: 14px;
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            overflow: hidden;
            border: 1px solid var(--border-secondary);
        }

        .message-text table th,
        .message-text table td {
            border: 1px solid var(--border-secondary);
            padding: 12px 16px;
            text-align: left;
        }

        .message-text table th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--text-primary);
            font-weight: 600;
            text-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        }

        .message-text table tr:nth-child(even) {
            background: rgba(0, 255, 255, 0.05);
        }

        .message-text table tr:hover {
            background: rgba(0, 255, 255, 0.1);
            transform: scale(1.01);
            transition: all var(--transition-normal);
        }

        /* 隐藏思考过程 */
        .thinking-process {
            display: none;
        }

        /* 复制成功提示 */
        .copy-toast {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--bg-card);
            color: var(--text-primary);
            padding: 16px 24px;
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-primary);
            box-shadow: var(--shadow-neon);
            z-index: 9999;
            animation: toastSlideIn 0.3s ease-out;
        }

        @keyframes toastSlideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }

        /* 浮动粒子效果 */
        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--primary-color);
            border-radius: 50%;
            animation: floatParticle 10s linear infinite;
            opacity: 0.3;
        }

        @keyframes floatParticle {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.3;
            }
            90% {
                opacity: 0.3;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <!-- 加载屏幕 -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-logo">DeepSeek Chat</div>
        <div class="loading-text">正在启动未来聊天体验...</div>
        <div class="loading-bar">
            <div class="loading-progress"></div>
        </div>
    </div>

    <!-- 浮动粒子效果 -->
    <div class="floating-particles" id="floatingParticles"></div>

    <!-- 主题切换按钮 -->
    <button class="theme-toggle" id="themeToggle" title="切换主题">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    </button>

    <!-- 连接状态指示器 -->
    <div class="connection-status" id="connectionStatus">
        <div class="status-indicator status-connected" id="statusIndicator"></div>
        <span id="statusText">已连接</span>
    </div>

    <!-- 全屏动画容器 -->
    <div class="fullscreen-animation" id="fullscreenAnimation"></div>

    <!-- 侧边栏 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <button class="new-chat-btn" id="newChatBtn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 4V20M4 12H20" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
                新建对话
            </button>
        </div>
        <div class="chat-history" id="chatHistory">
            <!-- 聊天历史将通过JavaScript动态生成 -->
        </div>
        <div class="sidebar-footer">
            <div class="user-profile">
                <div class="user-avatar">智</div>
                <div class="user-name">智能用户</div>
                <div class="user-menu">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13Z" fill="currentColor"/>
                        <path d="M12 6C12.5523 6 13 5.55228 13 5C13 4.44772 12.5523 4 12 4C11.4477 4 11 4.44772 11 5C11 5.55228 11.4477 6 12 6Z" fill="currentColor"/>
                        <path d="M12 20C12.5523 20 13 19.5523 13 19C13 18.4477 12.5523 18 12 18C11.4477 18 11 18.4477 11 19C11 19.5523 11.4477 20 12 20Z" fill="currentColor"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- 主聊天区域 -->
    <div class="main-content">
        <div class="chat-header">
            <button id="menuToggle" style="display: none;" class="chat-action-btn">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
            <div class="chat-title" id="currentChatTitle">✨ 新的对话</div>
            <div class="chat-actions">
                <button class="chat-action-btn" title="清空对话" id="clearChatBtn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19 7L18.1327 19.1425C18.0579 20.1891 17.187 21 16.1378 21H7.86224C6.81296 21 5.94208 20.1891 5.86732 19.1425L5 7M10 11V17M14 11V17M15 7V4C15 3.44772 14.5523 3 14 3H10C9.44772 3 9 3.44772 9 4V7M4 7H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
                <button class="chat-action-btn" title="导出对话" id="exportChatBtn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15M7 10L12 15M12 15L17 10M12 15V3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
        </div>

        <div class="chat-container" id="chatContainer">
            <!-- 聊天消息将通过JavaScript动态生成 -->
            <div class="welcome-message">
                <div class="message">
                    <div class="avatar bot-avatar">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M8 14C8 14 9.5 16 12 16C14.5 16 16 14 16 14M9 9H9.01M15 9H15.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="message-content">
                        <div class="message-role">DeepSeek Chat</div>
                        <div class="message-text">
                            <p>🚀 欢迎来到未来的聊天体验！我是DeepSeek Chat，你的超级智能AI助手。</p>
                            <p>💡 我可以帮助你：</p>
                            <ul>
                                <li>🎯 解答复杂问题和提供专业建议</li>
                                <li>💻 编写和优化代码</li>
                                <li>📝 创作文档和内容</li>
                                <li>🔍 分析数据和信息</li>
                                <li>🎨 创意思维和头脑风暴</li>
                            </ul>
                            <p>✨ 开始你的智能对话之旅吧！</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="input-container">
            <div class="input-box">
                <div class="textarea-wrapper">
                    <textarea class="chat-input" id="chatInput" placeholder="💬 输入你的消息，开启智能对话..." rows="1"></textarea>
                    <button class="send-btn" id="sendBtn" disabled>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M22 2L11 13M22 2L15 22L11 13M11 13L2 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // DOM元素
            const chatInput = document.getElementById('chatInput');
            const sendBtn = document.getElementById('sendBtn');
            const chatContainer = document.getElementById('chatContainer');
            const newChatBtn = document.getElementById('newChatBtn');
            const clearChatBtn = document.getElementById('clearChatBtn');
            const exportChatBtn = document.getElementById('exportChatBtn');
            const chatHistory = document.getElementById('chatHistory');
            const currentChatTitle = document.getElementById('currentChatTitle');
            const menuToggle = document.getElementById('menuToggle');
            const sidebar = document.getElementById('sidebar');
            const themeToggle = document.getElementById('themeToggle');
            const connectionStatus = document.getElementById('connectionStatus');
            const statusIndicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            const loadingScreen = document.getElementById('loadingScreen');
            const fullscreenAnimation = document.getElementById('fullscreenAnimation');
            const floatingParticles = document.getElementById('floatingParticles');

            // 应用状态
            let chats = JSON.parse(localStorage.getItem('deepseek-chats')) || [];
            let currentChatId = chats.length > 0 ? chats[chats.length - 1].id : null;
            let isWaitingForResponse = false;
            let isStreaming = false;
            let currentStreamingMessage = null;
            let eventSource = null;
            let isDarkTheme = true;
            let connectionState = 'connected'; // connected, connecting, disconnected

            // 初始化应用
            function initializeApp() {
                // 隐藏加载屏幕
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 3000);

                // 创建浮动粒子
                createFloatingParticles();

                // 初始化响应式设计
                initializeResponsive();

                // 初始化聊天历史
                initChatHistory();

                // 加载当前聊天或创建新聊天
                if (currentChatId) {
                    loadChat(currentChatId);
                } else {
                    createNewChat();
                }

                // 更新连接状态
                updateConnectionStatus('connected');
            }

            // 创建浮动粒子效果
            function createFloatingParticles() {
                for (let i = 0; i < 20; i++) {
                    setTimeout(() => {
                        createParticle();
                    }, i * 500);
                }

                // 持续创建粒子
                setInterval(createParticle, 2000);
            }

            function createParticle() {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 2 + 's';
                particle.style.animationDuration = (8 + Math.random() * 4) + 's';

                const colors = ['var(--primary-color)', 'var(--secondary-color)', 'var(--accent-color)'];
                particle.style.background = colors[Math.floor(Math.random() * colors.length)];

                floatingParticles.appendChild(particle);

                // 移除粒子
                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                }, 12000);
            }

            // 响应式设计初始化
            function initializeResponsive() {
                if (window.innerWidth <= 768) {
                    menuToggle.style.display = 'block';
                    menuToggle.addEventListener('click', () => {
                        sidebar.classList.toggle('open');
                    });
                }

                window.addEventListener('resize', () => {
                    if (window.innerWidth <= 768) {
                        menuToggle.style.display = 'block';
                    } else {
                        menuToggle.style.display = 'none';
                        sidebar.classList.remove('open');
                    }
                });
            }

            // 更新连接状态
            function updateConnectionStatus(status) {
                connectionState = status;
                statusIndicator.className = `status-indicator status-${status}`;

                const statusMessages = {
                    connected: '已连接',
                    connecting: '连接中...',
                    disconnected: '连接断开'
                };

                statusText.textContent = statusMessages[status] || '未知状态';
            }

            // 初始化聊天历史
            function initChatHistory() {
                chatHistory.innerHTML = '';
                chats.forEach((chat, index) => {
                    const chatItem = document.createElement('div');
                    chatItem.className = `chat-item ${chat.id === currentChatId ? 'active' : ''}`;
                    chatItem.setAttribute('data-id', chat.id);
                    chatItem.innerHTML = `
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span>${chat.title}</span>
                    `;

                    // 添加动画延迟
                    chatItem.style.animationDelay = `${index * 0.1}s`;
                    chatItem.style.animation = 'messageSlideIn 0.5s ease-out forwards';

                    chatItem.addEventListener('click', () => loadChat(chat.id));
                    chatHistory.appendChild(chatItem);
                });
            }

            // 加载聊天
            function loadChat(chatId) {
                currentChatId = chatId;
                const chat = chats.find(c => c.id === chatId);
                if (chat) {
                    currentChatTitle.textContent = `✨ ${chat.title}`;
                    renderMessages(chat.messages);

                    // 更新聊天历史中的活动状态
                    document.querySelectorAll('.chat-item').forEach(item => {
                        item.classList.remove('active');
                    });
                    document.querySelector(`.chat-item[data-id="${chatId}"]`)?.classList.add('active');
                }

                // 移动端关闭侧边栏
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('open');
                }

                // 滚动到底部
                scrollToBottom();
            }

            // 渲染消息
            function renderMessages(messages) {
                chatContainer.innerHTML = '';

                if (messages.length === 0) {
                    // 显示欢迎消息
                    chatContainer.innerHTML = `
                        <div class="message">
                            <div class="avatar bot-avatar">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M8 14C8 14 9.5 16 12 16C14.5 16 16 14 16 14M9 9H9.01M15 9H15.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <div class="message-content">
                                <div class="message-role">DeepSeek Chat</div>
                                <div class="message-text">
                                    <p>🚀 欢迎来到未来的聊天体验！我是DeepSeek Chat，你的超级智能AI助手。</p>
                                    <p>💡 我可以帮助你：</p>
                                    <ul>
                                        <li>🎯 解答复杂问题和提供专业建议</li>
                                        <li>💻 编写和优化代码</li>
                                        <li>📝 创作文档和内容</li>
                                        <li>🔍 分析数据和信息</li>
                                        <li>🎨 创意思维和头脑风暴</li>
                                    </ul>
                                    <p>✨ 开始你的智能对话之旅吧！</p>
                                </div>
                            </div>
                        </div>
                    `;
                    return;
                }

                messages.forEach((msg, index) => {
                    const messageDiv = createMessageElement(msg, index);
                    chatContainer.appendChild(messageDiv);
                });

                // 滚动到底部
                scrollToBottom();
            }

            // 创建消息元素
            function createMessageElement(msg, index = 0) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message';
                messageDiv.style.animationDelay = `${index * 0.1}s`;

                if (msg.role === 'user') {
                    messageDiv.innerHTML = `
                        <div class="avatar user-avatar">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="message-content">
                            <div class="message-role">智能用户</div>
                            <div class="message-text">${escapeHtml(msg.content)}</div>
                        </div>
                    `;
                } else {
                    messageDiv.innerHTML = `
                        <div class="avatar bot-avatar">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M8 14C8 14 9.5 16 12 16C14.5 16 16 14 16 14M9 9H9.01M15 9H15.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="message-content">
                            <div class="message-role">DeepSeek Chat</div>
                            <div class="message-text">${formatMessage(msg.content)}</div>
                            <div class="message-actions">
                                <button class="message-action copy-btn" onclick="copyMessage(this)">
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M16 4H18C18.5304 4 19.0391 4.21071 19.4142 4.58579C19.7893 4.96086 20 5.46957 20 6V18C20 18.5304 19.7893 19.0391 19.4142 19.4142C19.0391 19.7893 18.5304 20 18 20H6C5.46957 20 4.96086 19.7893 4.58579 19.4142C4.21071 19.0391 4 18.5304 4 18V6C4 5.46957 4.21071 4.96086 4.58579 4.58579C4.96086 4.21071 5.46957 4 6 4H8M16 4C16 2.89543 15.1046 2 14 2H10C8.89543 2 8 2.89543 8 4M16 4C16 5.10457 15.1046 6 14 6H10C8.89543 6 8 5.10457 8 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    复制
                                </button>
                                <button class="message-action share-btn" onclick="shareMessage(this)">
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M18 8C19.6569 8 21 6.65685 21 5C21 3.34315 19.6569 2 18 2C16.3431 2 15 3.34315 15 5C15 6.65685 16.3431 8 18 8Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M6 15C7.65685 15 9 13.6569 9 12C9 10.3431 7.65685 9 6 9C4.34315 9 3 10.3431 3 12C3 13.6569 4.34315 15 6 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M18 22C19.6569 22 21 20.6569 21 19C21 17.3431 19.6569 16 18 16C16.3431 16 15 17.3431 15 19C15 20.6569 16.3431 22 18 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M8.59 13.51L15.42 17.49M15.41 6.51L8.59 10.49" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    分享
                                </button>
                                <button class="message-action like-btn" onclick="likeMessage(this)">
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M14 9V5C14 4.46957 13.7893 3.96086 13.4142 3.58579C13.0391 3.21071 12.5304 3 12 3H11C10.4696 3 9.96086 3.21071 9.58579 3.58579C9.21071 3.96086 9 4.46957 9 5V9H4C3.46957 9 2.96086 9.21071 2.58579 9.58579C2.21071 9.96086 2 10.4696 2 11V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18.28C18.7623 22.0055 19.2304 21.8364 19.5979 21.524C19.9654 21.2116 20.2077 20.7769 20.28 20.3L21.66 11.3C21.7035 11.0134 21.6842 10.7207 21.6033 10.4423C21.5225 10.1638 21.3821 9.90629 21.1919 9.68751C21.0016 9.46873 20.7661 9.29393 20.5016 9.17522C20.2371 9.0565 19.9499 8.99672 19.66 9H14Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    赞
                                </button>
                                <button class="message-action dislike-btn" onclick="dislikeMessage(this)">
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M10 15V19C10 19.5304 10.2107 20.0391 10.5858 20.4142C10.9609 20.7893 11.4696 21 12 21H13C13.5304 21 14.0391 20.7893 14.4142 20.4142C14.7893 20.0391 15 19.5304 15 19V15H20C20.5304 15 21.0391 14.7893 21.4142 14.4142C21.7893 14.0391 22 13.5304 22 13V4C22 3.46957 21.7893 2.96086 21.4142 2.58579C21.0391 2.21071 20.5304 2 20 2H5.72C5.23767 1.99448 4.76962 2.16361 4.40213 2.47596C4.03464 2.78831 3.79234 3.22305 3.72 3.7L2.34 12.7C2.29649 12.9866 2.31583 13.2793 2.39667 13.5577C2.47751 13.8362 2.61793 14.0937 2.80816 14.3125C2.99839 14.5313 3.23394 14.7061 3.49843 14.8248C3.76291 14.9435 4.05011 15.0033 4.34 15H10Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    踩
                                </button>
                            </div>
                        </div>
                    `;
                }

                return messageDiv;
            }

            // HTML转义函数
            function escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            // 格式化消息内容（增强的Markdown转换）
            function formatMessage(content) {
                // 移除<think>标签内容
                content = content.replace(/<think>.*?<\/think>/gs, '');

                // 转义HTML
                content = escapeHtml(content);

                // 增强的Markdown转换
                // 标题
                content = content.replace(/^# (.*$)/gm, '<h1>$1</h1>');
                content = content.replace(/^## (.*$)/gm, '<h2>$1</h2>');
                content = content.replace(/^### (.*$)/gm, '<h3>$1</h3>');
                content = content.replace(/^#### (.*$)/gm, '<h4>$1</h4>');

                // 粗体和斜体
                content = content.replace(/\*\*\*(.*?)\*\*\*/g, '<strong><em>$1</em></strong>');
                content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                content = content.replace(/__(.*?)__/g, '<strong>$1</strong>');
                content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');
                content = content.replace(/_(.*?)_/g, '<em>$1</em>');

                // 代码块
                content = content.replace(/```([a-z]*)\n([\s\S]*?)\n```/g, '<pre><code class="language-$1">$2</code></pre>');
                content = content.replace(/`(.*?)`/g, '<code>$1</code>');

                // 链接
                content = content.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');

                // 图片
                content = content.replace(/!\[(.*?)\]\((.*?)\)/g, '<img src="$2" alt="$1" style="max-width: 100%; border-radius: 8px;">');

                // 列表
                content = content.replace(/^\s*\*\s(.*$)/gm, '<li>$1</li>');
                content = content.replace(/^\s*-\s(.*$)/gm, '<li>$1</li>');
                content = content.replace(/^\s*\+\s(.*$)/gm, '<li>$1</li>');
                content = content.replace(/^\s*\d+\.\s(.*$)/gm, '<li>$1</li>');

                // 包装列表项
                content = content.replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>');

                // 引用
                content = content.replace(/^>\s(.*$)/gm, '<blockquote>$1</blockquote>');

                // 分隔线
                content = content.replace(/^---$/gm, '<hr>');
                content = content.replace(/^\*\*\*$/gm, '<hr>');

                // 换行
                content = content.replace(/\n/g, '<br>');

                return content;
            }

            // 创建新聊天
            function createNewChat() {
                const newChat = {
                    id: Date.now().toString(),
                    title: '新的对话',
                    messages: [],
                    createdAt: new Date().toISOString()
                };

                chats.push(newChat);
                currentChatId = newChat.id;
                currentChatTitle.textContent = `✨ ${newChat.title}`;

                saveChats();
                initChatHistory();
                renderMessages([]);

                // 移动端关闭侧边栏
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('open');
                }

                // 聚焦输入框
                chatInput.focus();

                // 添加创建动画
                chatContainer.style.animation = 'messageSlideIn 0.5s ease-out';
            }

            // 保存聊天
            function saveChats() {
                localStorage.setItem('deepseek-chats', JSON.stringify(chats));
            }

            // 滚动到底部
            function scrollToBottom() {
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }

            // 消息操作函数
            window.copyMessage = function(button) {
                const messageText = button.closest('.message-content').querySelector('.message-text');
                const text = messageText.innerText || messageText.textContent;

                navigator.clipboard.writeText(text).then(() => {
                    showToast('✅ 消息已复制到剪贴板');

                    // 添加复制动画
                    button.style.transform = 'scale(1.2)';
                    button.style.color = 'var(--success-color)';
                    setTimeout(() => {
                        button.style.transform = '';
                        button.style.color = '';
                    }, 300);
                }).catch(() => {
                    showToast('❌ 复制失败，请手动复制');
                });
            };

            window.shareMessage = function(button) {
                const messageText = button.closest('.message-content').querySelector('.message-text');
                const text = messageText.innerText || messageText.textContent;

                if (navigator.share) {
                    navigator.share({
                        title: 'DeepSeek Chat 对话',
                        text: text
                    });
                } else {
                    copyMessage(button);
                    showToast('🔗 消息已复制，可以分享给朋友');
                }
            };

            window.likeMessage = function(button) {
                createFullscreenAnimation('like', button);
                button.style.color = 'var(--success-color)';
                showToast('👍 感谢你的反馈！');
            };

            window.dislikeMessage = function(button) {
                createFullscreenAnimation('dislike', button);
                button.style.color = 'var(--error-color)';
                showToast('👎 我们会继续改进');
            };

            // 创建全屏动画效果
            function createFullscreenAnimation(type, button) {
                const rect = button.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;

                const explosion = document.createElement('div');
                explosion.className = `${type}-explosion`;
                explosion.style.left = centerX + 'px';
                explosion.style.top = centerY + 'px';

                fullscreenAnimation.appendChild(explosion);

                setTimeout(() => {
                    if (explosion.parentNode) {
                        explosion.parentNode.removeChild(explosion);
                    }
                }, 1000);
            }

            // 显示提示消息
            function showToast(message) {
                const toast = document.createElement('div');
                toast.className = 'copy-toast';
                toast.textContent = message;
                document.body.appendChild(toast);

                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 2000);
            }

            // 添加用户消息
            function addUserMessage(content) {
                if (!currentChatId) {
                    createNewChat();
                }

                const chat = chats.find(c => c.id === currentChatId);
                if (!chat) return;

                const message = {
                    role: 'user',
                    content: content,
                    timestamp: new Date().toISOString()
                };

                chat.messages.push(message);

                // 如果这是第一条消息，更新聊天标题
                if (chat.messages.length === 1) {
                    chat.title = content.length > 20 ? content.substring(0, 20) + '...' : content;
                    currentChatTitle.textContent = `✨ ${chat.title}`;
                }

                saveChats();

                // 添加用户消息到界面
                const messageDiv = createMessageElement(message);
                chatContainer.appendChild(messageDiv);
                scrollToBottom();

                return chat;
            }

            // 创建流式AI消息
            function createStreamingMessage() {
                const message = {
                    role: 'assistant',
                    content: '',
                    timestamp: new Date().toISOString()
                };

                const messageDiv = createMessageElement(message);
                const messageText = messageDiv.querySelector('.message-text');
                messageText.classList.add('streaming-text');

                chatContainer.appendChild(messageDiv);
                scrollToBottom();

                return { messageDiv, messageText, message };
            }

            // 更新流式消息内容
            function updateStreamingMessage(streamingData, newContent) {
                streamingData.message.content += newContent;
                streamingData.messageText.innerHTML = formatMessage(streamingData.message.content);
                scrollToBottom();
            }

            // 完成流式消息
            function finishStreamingMessage(streamingData) {
                streamingData.messageText.classList.remove('streaming-text');

                const chat = chats.find(c => c.id === currentChatId);
                if (chat) {
                    chat.messages.push(streamingData.message);
                    saveChats();
                    initChatHistory();
                }
            }

            // 显示高级打字指示器
            function showTypingIndicator() {
                const typingDiv = document.createElement('div');
                typingDiv.className = 'message';
                typingDiv.innerHTML = `
                    <div class="avatar bot-avatar">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M8 14C8 14 9.5 16 12 16C14.5 16 16 14 16 14M9 9H9.01M15 9H15.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="message-content">
                        <div class="message-role">DeepSeek Chat</div>
                        <div class="typing-indicator">
                            <span class="typing-text">正在思考中</span>
                            <div class="typing-dots">
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                        </div>
                    </div>
                `;

                chatContainer.appendChild(typingDiv);
                scrollToBottom();

                return typingDiv;
            }

            // 移除打字指示器
            function removeTypingIndicator(indicator) {
                if (indicator && indicator.parentNode) {
                    indicator.parentNode.removeChild(indicator);
                }
            }

            // 发送消息到Ollama API（支持流式响应）
            async function sendToOllama(message) {
                const chat = addUserMessage(message);
                updateConnectionStatus('connecting');

                isWaitingForResponse = true;
                isStreaming = true;
                sendBtn.disabled = true;

                try {
                    // 使用流式API
                    const response = await fetch('http://localhost:11434/api/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            model: 'qwen2.5:7b',
                            messages: chat.messages,
                            stream: true
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    updateConnectionStatus('connected');

                    // 创建流式消息
                    const streamingData = createStreamingMessage();

                    // 读取流式响应
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();

                    while (true) {
                        const { done, value } = await reader.read();

                        if (done) break;

                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n').filter(line => line.trim());

                        for (const line of lines) {
                            try {
                                const data = JSON.parse(line);
                                if (data.message && data.message.content) {
                                    updateStreamingMessage(streamingData, data.message.content);
                                }

                                if (data.done) {
                                    finishStreamingMessage(streamingData);
                                    break;
                                }
                            } catch (e) {
                                // 忽略JSON解析错误
                                console.warn('JSON parse error:', e);
                            }
                        }
                    }

                } catch (error) {
                    console.error('Error calling Ollama API:', error);
                    updateConnectionStatus('disconnected');

                    // 显示错误消息
                    const errorMessage = {
                        role: 'assistant',
                        content: '🚫 抱歉，连接出现问题。请检查网络连接或Ollama服务状态。',
                        timestamp: new Date().toISOString()
                    };

                    const errorDiv = createMessageElement(errorMessage);
                    chatContainer.appendChild(errorDiv);

                    chat.messages.push(errorMessage);
                    saveChats();

                    // 自动重连
                    setTimeout(() => {
                        updateConnectionStatus('connected');
                    }, 3000);

                } finally {
                    isWaitingForResponse = false;
                    isStreaming = false;
                    sendBtn.disabled = chatInput.value.trim() === '';
                }
            }

            // 事件监听器设置
            function setupEventListeners() {
                // 输入框自动调整高度
                chatInput.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = Math.min(this.scrollHeight, 200) + 'px';
                    sendBtn.disabled = this.value.trim() === '' || isWaitingForResponse;
                });

                // 按Enter发送消息，Shift+Enter换行
                chatInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey && !isWaitingForResponse) {
                        e.preventDefault();
                        const message = this.value.trim();
                        if (message) {
                            sendToOllama(message);
                            this.value = '';
                            this.style.height = 'auto';
                            sendBtn.disabled = true;
                        }
                    }
                });

                // 点击发送按钮
                sendBtn.addEventListener('click', function() {
                    if (isWaitingForResponse) return;

                    const message = chatInput.value.trim();
                    if (message) {
                        sendToOllama(message);
                        chatInput.value = '';
                        chatInput.style.height = 'auto';
                        sendBtn.disabled = true;
                    }
                });

                // 新聊天按钮
                newChatBtn.addEventListener('click', createNewChat);

                // 清空聊天按钮
                clearChatBtn.addEventListener('click', function() {
                    if (confirm('🗑️ 确定要清空当前对话吗？此操作无法撤销。')) {
                        const chat = chats.find(c => c.id === currentChatId);
                        if (chat) {
                            chat.messages = [];
                            saveChats();
                            renderMessages([]);
                            showToast('✅ 对话已清空');
                        }
                    }
                });

                // 导出聊天按钮
                exportChatBtn.addEventListener('click', function() {
                    const chat = chats.find(c => c.id === currentChatId);
                    if (chat && chat.messages.length > 0) {
                        exportChat(chat);
                    } else {
                        showToast('📝 当前对话为空，无法导出');
                    }
                });

                // 主题切换按钮
                themeToggle.addEventListener('click', function() {
                    isDarkTheme = !isDarkTheme;
                    // 这里可以添加主题切换逻辑
                    showToast(isDarkTheme ? '🌙 已切换到深色主题' : '☀️ 已切换到浅色主题');
                });
            }

            // 导出聊天功能
            function exportChat(chat) {
                const content = chat.messages.map(msg => {
                    const role = msg.role === 'user' ? '用户' : 'DeepSeek Chat';
                    const time = new Date(msg.timestamp).toLocaleString('zh-CN');
                    return `[${time}] ${role}:\n${msg.content}\n`;
                }).join('\n');

                const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `DeepSeek_Chat_${chat.title}_${new Date().toISOString().split('T')[0]}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                showToast('📄 对话已导出到文件');
            }

            // 初始化应用
            setupEventListeners();
            initializeApp();
        });
    </script>
</body>
</html>