<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek Chat</title>
    <style>
        :root {
            --primary-color: #2d6df6;
            --primary-hover: #1a56d8;
            --text-color: #333;
            --light-text: #666;
            --lighter-text: #999;
            --bg-color: #fff;
            --sidebar-bg: #f7f7f8;
            --chat-bg: #f7f7f8;
            --input-bg: #fff;
            --border-color: #e5e5e6;
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            --radius: 8px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }

        body {
            color: var(--text-color);
            background-color: var(--bg-color);
            display: flex;
            height: 100vh;
            overflow: hidden;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            background-color: var(--sidebar-bg);
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            height: 100vh;
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .new-chat-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--radius);
            padding: 8px 12px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: background-color 0.2s;
        }

        .new-chat-btn:hover {
            background-color: var(--primary-hover);
        }

        .chat-history {
            flex: 1;
            overflow-y: auto;
            padding: 8px;
        }

        .chat-item {
            padding: 10px 12px;
            border-radius: var(--radius);
            cursor: pointer;
            margin-bottom: 4px;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chat-item:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .chat-item.active {
            background-color: rgba(45, 109, 246, 0.1);
        }

        .sidebar-footer {
            padding: 16px;
            border-top: 1px solid var(--border-color);
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .user-name {
            font-size: 14px;
            flex: 1;
        }

        .user-menu {
            cursor: pointer;
        }

        /* 主聊天区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100vh;
            background-color: var(--chat-bg);
        }

        .chat-header {
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: var(--bg-color);
        }

        .chat-title {
            font-weight: 600;
            font-size: 16px;
        }

        .chat-actions {
            display: flex;
            gap: 12px;
        }

        .chat-action-btn {
            background: none;
            border: none;
            cursor: pointer;
            color: var(--light-text);
            font-size: 16px;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .message {
            display: flex;
            gap: 16px;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
        }

        .avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .user-avatar {
            background-color: #e3f2fd;
            color: var(--primary-color);
        }

        .bot-avatar {
            background-color: #f0f7ff;
        }

        .message-content {
            flex: 1;
            padding-top: 4px;
        }

        .message-role {
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 16px;
        }

        .message-text {
            line-height: 1.6;
            font-size: 15px;
        }

        .message-text pre {
            background-color: #f6f8fa;
            padding: 16px;
            border-radius: 6px;
            overflow-x: auto;
            margin: 10px 0;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
            font-size: 14px;
        }

        .message-text code {
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
            background-color: #f6f8fa;
            padding: 2px 4px;
            border-radius: 4px;
            font-size: 14px;
        }

        .message-text blockquote {
            border-left: 3px solid #ddd;
            padding-left: 12px;
            margin: 8px 0;
            color: #666;
        }

        .message-actions {
            display: flex;
            gap: 12px;
            margin-top: 8px;
        }

        .message-action {
            background: none;
            border: none;
            cursor: pointer;
            color: var(--light-text);
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .message-action:hover {
            color: var(--primary-color);
        }

        .input-container {
            padding: 16px;
            border-top: 1px solid var(--border-color);
            background-color: var(--bg-color);
        }

        .input-box {
            max-width: 800px;
            margin: 0 auto;
            position: relative;
        }

        .textarea-wrapper {
            position: relative;
        }

        .chat-input {
            width: 100%;
            min-height: 56px;
            max-height: 200px;
            padding: 12px 52px 12px 16px;
            border-radius: var(--radius);
            border: 1px solid var(--border-color);
            background-color: var(--input-bg);
            resize: none;
            font-size: 15px;
            line-height: 1.5;
            outline: none;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        }

        .chat-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(45, 109, 246, 0.2);
        }

        .send-btn {
            position: absolute;
            right: 12px;
            bottom: 12px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
        }

        .send-btn:hover {
            background-color: var(--primary-hover);
        }

        .send-btn:disabled {
            background-color: var(--light-text);
            cursor: not-allowed;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 4px;
            margin-top: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background-color: var(--light-text);
            border-radius: 50%;
            animation: typingAnimation 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) {
            animation-delay: 0s;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typingAnimation {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.6;
            }
            30% {
                transform: translateY(-4px);
                opacity: 1;
            }
        }

        /* 移动端响应式 */
        @media (max-width: 768px) {
            .sidebar {
                position: absolute;
                z-index: 100;
                transform: translateX(-100%);
            }
            .sidebar.open {
                transform: translateX(0);
            }
            .main-content {
                width: 100%;
            }
            .message {
                max-width: 100%;
            }
        }

        /* Markdown 表格样式 */
        .message-text table {
            border-collapse: collapse;
            width: 100%;
            margin: 16px 0;
            font-size: 14px;
        }
        
        .message-text table th, 
        .message-text table td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }
        
        .message-text table th {
            background-color: #f2f2f2;
            font-weight: 600;
        }
        
        .message-text table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        /* 隐藏思考过程 */
        .thinking-process {
            display: none;
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <button class="new-chat-btn" id="newChatBtn">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 4V20M4 12H20" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
                新对话
            </button>
        </div>
        <div class="chat-history" id="chatHistory">
            <!-- 聊天历史将通过JavaScript动态生成 -->
        </div>
        <div class="sidebar-footer">
            <div class="user-profile">
                <div class="user-avatar">U</div>
                <div class="user-name">用户</div>
                <div class="user-menu">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13Z" fill="currentColor" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M12 6C12.5523 6 13 5.55228 13 5C13 4.44772 12.5523 4 12 4C11.4477 4 11 4.44772 11 5C11 5.55228 11.4477 6 12 6Z" fill="currentColor" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M12 20C12.5523 20 13 19.5523 13 19C13 18.4477 12.5523 18 12 18C11.4477 18 11 18.4477 11 19C11 19.5523 11.4477 20 12 20Z" fill="currentColor" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- 主聊天区域 -->
    <div class="main-content">
        <div class="chat-header">
            <button id="menuToggle" style="display: none;">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
            <div class="chat-title" id="currentChatTitle">新对话</div>
            <div class="chat-actions">
                <button class="chat-action-btn" title="清空对话" id="clearChatBtn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19 7L18.1327 19.1425C18.0579 20.1891 17.187 21 16.1378 21H7.86224C6.81296 21 5.94208 20.1891 5.86732 19.1425L5 7M10 11V17M14 11V17M15 7V4C15 3.44772 14.5523 3 14 3H10C9.44772 3 9 3.44772 9 4V7M4 7H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
        </div>

        <div class="chat-container" id="chatContainer">
            <!-- 聊天消息将通过JavaScript动态生成 -->
            <div class="welcome-message">
                <div class="message">
                    <div class="avatar bot-avatar">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M8 14C8 14 9.5 16 12 16C14.5 16 16 14 16 14M9 9H9.01M15 9H15.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="message-content">
                        <div class="message-role">DeepSeek Chat</div>
                        <div class="message-text">
                            <p>你好！我是DeepSeek Chat，你的智能AI助手。我可以帮助你解答问题、提供建议、分析数据等。</p>
                            <p>你可以问我任何问题，比如：</p>
                            <ul>
                                <li>如何学习编程？</li>
                                <li>解释量子计算的基本概念</li>
                                <li>帮我写一封求职信</li>
                                <li>用Python写一个快速排序算法</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="input-container">
            <div class="input-box">
                <div class="textarea-wrapper">
                    <textarea class="chat-input" id="chatInput" placeholder="输入消息..." rows="1"></textarea>
                    <button class="send-btn" id="sendBtn" disabled>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M22 2L11 13M22 2L15 22L11 13M11 13L2 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // DOM元素
            const chatInput = document.getElementById('chatInput');
            const sendBtn = document.getElementById('sendBtn');
            const chatContainer = document.getElementById('chatContainer');
            const newChatBtn = document.getElementById('newChatBtn');
            const clearChatBtn = document.getElementById('clearChatBtn');
            const chatHistory = document.getElementById('chatHistory');
            const currentChatTitle = document.getElementById('currentChatTitle');
            const menuToggle = document.getElementById('menuToggle');
            const sidebar = document.getElementById('sidebar');

            // 响应式设计 - 移动端菜单切换
            if (window.innerWidth <= 768) {
                menuToggle.style.display = 'block';
                menuToggle.addEventListener('click', () => {
                    sidebar.classList.toggle('open');
                });
            }

            window.addEventListener('resize', () => {
                if (window.innerWidth <= 768) {
                    menuToggle.style.display = 'block';
                } else {
                    menuToggle.style.display = 'none';
                    sidebar.classList.remove('open');
                }
            });

            // 聊天数据
            let chats = JSON.parse(localStorage.getItem('deepseek-chats')) || [];
            let currentChatId = chats.length > 0 ? chats[chats.length - 1].id : null;
            let isWaitingForResponse = false;

            // 初始化聊天历史
            function initChatHistory() {
                chatHistory.innerHTML = '';
                chats.forEach(chat => {
                    const chatItem = document.createElement('div');
                    chatItem.className = `chat-item ${chat.id === currentChatId ? 'active' : ''}`;
                    chatItem.innerHTML = `
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        ${chat.title}
                    `;
                    chatItem.addEventListener('click', () => loadChat(chat.id));
                    chatHistory.appendChild(chatItem);
                });
            }

            // 加载聊天
            function loadChat(chatId) {
                currentChatId = chatId;
                const chat = chats.find(c => c.id === chatId);
                if (chat) {
                    currentChatTitle.textContent = chat.title;
                    renderMessages(chat.messages);
                    
                    // 更新聊天历史中的活动状态
                    document.querySelectorAll('.chat-item').forEach(item => {
                        item.classList.remove('active');
                    });
                    document.querySelector(`.chat-item[data-id="${chatId}"]`)?.classList.add('active');
                }
                
                // 移动端关闭侧边栏
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('open');
                }
                
                // 滚动到底部
                scrollToBottom();
            }

            // 渲染消息
            function renderMessages(messages) {
                chatContainer.innerHTML = '';
                
                if (messages.length === 0) {
                    // 显示欢迎消息
                    chatContainer.innerHTML = `
                        <div class="message">
                            <div class="avatar bot-avatar">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M8 14C8 14 9.5 16 12 16C14.5 16 16 14 16 14M9 9H9.01M15 9H15.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <div class="message-content">
                                <div class="message-role">DeepSeek Chat</div>
                                <div class="message-text">
                                    <p>你好！我是DeepSeek Chat，你的智能AI助手。我可以帮助你解答问题、提供建议、分析数据等。</p>
                                    <p>你可以问我任何问题，比如：</p>
                                    <ul>
                                        <li>如何学习编程？</li>
                                        <li>解释量子计算的基本概念</li>
                                        <li>帮我写一封求职信</li>
                                        <li>用Python写一个快速排序算法</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    `;
                    return;
                }
                
                messages.forEach(msg => {
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'message';
                    
                    if (msg.role === 'user') {
                        messageDiv.innerHTML = `
                            <div class="avatar user-avatar">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <div class="message-content">
                                <div class="message-role">用户</div>
                                <div class="message-text">${msg.content}</div>
                            </div>
                        `;
                    } else {
                        messageDiv.innerHTML = `
                            <div class="avatar bot-avatar">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M8 14C8 14 9.5 16 12 16C14.5 16 16 14 16 14M9 9H9.01M15 9H15.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <div class="message-content">
                                <div class="message-role">DeepSeek Chat</div>
                                <div class="message-text">${formatMessage(msg.content)}</div>
                                <div class="message-actions">
                                    <button class="message-action">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M14 10C14 9.46957 14.2107 8.96086 14.5858 8.58579C14.9609 8.21071 15.4696 8 16 8H20C20.5304 8 21.0391 8.21071 21.4142 8.58579C21.7893 8.96086 22 9.46957 22 10V14C22 14.5304 21.7893 15.0391 21.4142 15.4142C21.0391 15.7893 20.5304 16 20 16H16C15.4696 16 14.9609 15.7893 14.5858 15.4142C14.2107 15.0391 14 14.5304 14 14V10Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M2 10C2 9.46957 2.21071 8.96086 2.58579 8.58579C2.96086 8.21071 3.46957 8 4 8H8C8.53043 8 9.03914 8.21071 9.41421 8.58579C9.78929 8.96086 10 9.46957 10 10V14C10 14.5304 9.78929 15.0391 9.41421 15.4142C9.03914 15.7893 8.53043 16 8 16H4C3.46957 16 2.96086 15.7893 2.58579 15.4142C2.21071 15.0391 2 14.5304 2 14V10Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M14 4H22M14 20H22M2 4H10M2 20H10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        复制
                                    </button>
                                    <button class="message-action">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M14 9V5C14 4.46957 13.7893 3.96086 13.4142 3.58579C13.0391 3.21071 12.5304 3 12 3H5C4.46957 3 3.96086 3.21071 3.58579 3.58579C3.21071 3.96086 3 4.46957 3 5V12C3 12.5304 3.21071 13.0391 3.58579 13.4142C3.96086 13.7893 4.46957 14 5 14H9M10 15H19C20.1046 15 21 15.8954 21 17V19C21 20.1046 20.1046 21 19 21H17C15.8954 21 15 20.1046 15 19V10C15 9.44772 15.4477 9 16 9H19C20.1046 9 21 9.89543 21 11V13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        分享
                                    </button>
                                    <button class="message-action">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M14 10C14 9.46957 14.2107 8.96086 14.5858 8.58579C14.9609 8.21071 15.4696 8 16 8H20C20.5304 8 21.0391 8.21071 21.4142 8.58579C21.7893 8.96086 22 9.46957 22 10V14C22 14.5304 21.7893 15.0391 21.4142 15.4142C21.0391 15.7893 20.5304 16 20 16H16C15.4696 16 14.9609 15.7893 14.5858 15.4142C14.2107 15.0391 14 14.5304 14 14V10Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M10 14H4C3.46957 14 2.96086 13.7893 2.58579 13.4142C2.21071 13.0391 2 12.5304 2 12V4C2 3.46957 2.21071 2.96086 2.58579 2.58579C2.96086 2.21071 3.46957 2 4 2H12C12.5304 2 13.0391 2.21071 13.4142 2.58579C13.7893 2.96086 14 3.46957 14 4V10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        赞
                                    </button>
                                    <button class="message-action">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M10 14H4C3.46957 14 2.96086 13.7893 2.58579 13.4142C2.21071 13.0391 2 12.5304 2 12V4C2 3.46957 2.21071 2.96086 2.58579 2.58579C2.96086 2.21071 3.46957 2 4 2H12C12.5304 2 13.0391 2.21071 13.4142 2.58579C13.7893 2.96086 14 3.46957 14 4V10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M14 10C14 9.46957 14.2107 8.96086 14.5858 8.58579C14.9609 8.21071 15.4696 8 16 8H20C20.5304 8 21.0391 8.21071 21.4142 8.58579C21.7893 8.96086 22 9.46957 22 10V14C22 14.5304 21.7893 15.0391 21.4142 15.4142C21.0391 15.7893 20.5304 16 20 16H16C15.4696 16 14.9609 15.7893 14.5858 15.4142C14.2107 15.0391 14 14.5304 14 14V10Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        踩
                                    </button>
                                </div>
                            </div>
                        `;
                    }
                    
                    chatContainer.appendChild(messageDiv);
                });
                
                // 滚动到底部
                scrollToBottom();
            }

            // 格式化消息内容（Markdown转换）
            function formatMessage(content) {
                // 移除<think>标签内容
                content = content.replace(/<think>.*?<\/think>/gs, '');
                
                // 简单的Markdown转换
                // 标题
                content = content.replace(/^# (.*$)/gm, '<h1>$1</h1>');
                content = content.replace(/^## (.*$)/gm, '<h2>$1</h2>');
                content = content.replace(/^### (.*$)/gm, '<h3>$1</h3>');
                
                // 粗体
                content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                content = content.replace(/__(.*?)__/g, '<strong>$1</strong>');
                
                // 斜体
                content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');
                content = content.replace(/_(.*?)_/g, '<em>$1</em>');
                
                // 代码块
                content = content.replace(/```([a-z]*)\n([\s\S]*?)\n```/g, '<pre><code class="language-$1">$2</code></pre>');
                content = content.replace(/`(.*?)`/g, '<code>$1</code>');
                
                // 链接
                content = content.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" target="_blank">$1</a>');
                
                // 图片
                content = content.replace(/!\[(.*?)\]\((.*?)\)/g, '<img src="$2" alt="$1">');
                
                // 列表
                content = content.replace(/^\s*\*\s(.*$)/gm, '<li>$1</li>');
                content = content.replace(/^\s*-\s(.*$)/gm, '<li>$1</li>');
                content = content.replace(/^\s*\+\s(.*$)/gm, '<li>$1</li>');
                
                // 引用
                content = content.replace(/^>\s(.*$)/gm, '<blockquote>$1</blockquote>');
                
                // 换行
                content = content.replace(/\n/g, '<br>');
                
                return content;
            }

            // 创建新聊天
            function createNewChat() {
                const newChat = {
                    id: Date.now().toString(),
                    title: '新对话',
                    messages: [],
                    createdAt: new Date().toISOString()
                };
                
                chats.push(newChat);
                currentChatId = newChat.id;
                currentChatTitle.textContent = newChat.title;
                
                saveChats();
                initChatHistory();
                renderMessages([]);
                
                // 移动端关闭侧边栏
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('open');
                }
                
                // 聚焦输入框
                chatInput.focus();
            }

            // 保存聊天
            function saveChats() {
                localStorage.setItem('deepseek-chats', JSON.stringify(chats));
            }

            // 滚动到底部
            function scrollToBottom() {
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }

            // 添加用户消息
            function addUserMessage(content) {
                if (!currentChatId) {
                    createNewChat();
                }
                
                const chat = chats.find(c => c.id === currentChatId);
                if (!chat) return;
                
                const message = {
                    role: 'user',
                    content: content,
                    timestamp: new Date().toISOString()
                };
                
                chat.messages.push(message);
                
                // 如果这是第一条消息，更新聊天标题
                if (chat.messages.length === 1) {
                    chat.title = content.length > 30 ? content.substring(0, 30) + '...' : content;
                    currentChatTitle.textContent = chat.title;
                }
                
                saveChats();
                renderMessages(chat.messages);
                
                return chat;
            }

            // 添加AI消息
            function addAIMessage(content) {
                const chat = chats.find(c => c.id === currentChatId);
                if (!chat) return;
                
                const message = {
                    role: 'assistant',
                    content: content,
                    timestamp: new Date().toISOString()
                };
                
                chat.messages.push(message);
                saveChats();
                renderMessages(chat.messages);
            }

            // 显示正在输入指示器
            function showTypingIndicator() {
                const typingDiv = document.createElement('div');
                typingDiv.className = 'message';
                typingDiv.innerHTML = `
                    <div class="avatar bot-avatar">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M8 14C8 14 9.5 16 12 16C14.5 16 16 14 16 14M9 9H9.01M15 9H15.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="message-content">
                        <div class="message-role">DeepSeek Chat</div>
                        <div class="typing-indicator">
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                    </div>
                `;
                
                chatContainer.appendChild(typingDiv);
                scrollToBottom();
                
                return typingDiv;
            }

            // 移除正在输入指示器
            function removeTypingIndicator(indicator) {
                if (indicator && indicator.parentNode) {
                    indicator.parentNode.removeChild(indicator);
                }
            }

            // 发送消息到Ollama API
            async function sendToOllama(message) {
                const chat = addUserMessage(message);
                const typingIndicator = showTypingIndicator();
                
                isWaitingForResponse = true;
                sendBtn.disabled = true;
                
                try {
                    const response = await fetch('http://localhost:11434/api/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            model: 'Qwen3:8b',
                            messages: chat.messages,
                            stream: false
                        })
                    });
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    
                    const data = await response.json();
                    const aiResponse = data.message.content;
                    
                    removeTypingIndicator(typingIndicator);
                    addAIMessage(aiResponse);
                } catch (error) {
                    console.error('Error calling Ollama API:', error);
                    removeTypingIndicator(typingIndicator);
                    
                    // 显示错误消息
                    const errorMessage = {
                        role: 'assistant',
                        content: '抱歉，我无法处理您的请求。请检查Ollama服务是否运行正常。',
                        timestamp: new Date().toISOString()
                    };
                    
                    chat.messages.push(errorMessage);
                    saveChats();
                    renderMessages(chat.messages);
                } finally {
                    isWaitingForResponse = false;
                    sendBtn.disabled = chatInput.value.trim() === '';
                }
            }

            // 输入框自动调整高度
            chatInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
                sendBtn.disabled = this.value.trim() === '' || isWaitingForResponse;
            });

            // 按Enter发送消息，Shift+Enter换行
            chatInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey && !isWaitingForResponse) {
                    e.preventDefault();
                    const message = this.value.trim();
                    if (message) {
                        sendToOllama(message);
                        this.value = '';
                        this.style.height = 'auto';
                        sendBtn.disabled = true;
                    }
                }
            });

            // 点击发送按钮
            sendBtn.addEventListener('click', function() {
                if (isWaitingForResponse) return;
                
                const message = chatInput.value.trim();
                if (message) {
                    sendToOllama(message);
                    chatInput.value = '';
                    chatInput.style.height = 'auto';
                    sendBtn.disabled = true;
                }
            });

            // 新聊天按钮
            newChatBtn.addEventListener('click', createNewChat);

            // 清空聊天按钮
            clearChatBtn.addEventListener('click', function() {
                if (confirm('确定要清空当前对话吗？')) {
                    const chat = chats.find(c => c.id === currentChatId);
                    if (chat) {
                        chat.messages = [];
                        saveChats();
                        renderMessages([]);
                    }
                }
            });

            // 初始化
            initChatHistory();
            if (currentChatId) {
                loadChat(currentChatId);
            } else {
                createNewChat();
            }
        });
    </script>
</body>
</html>